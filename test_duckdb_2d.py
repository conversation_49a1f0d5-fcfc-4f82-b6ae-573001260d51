#!/usr/bin/env python3

import duckdb
import json
import tempfile
import os

def test_duckdb_2d_array():
    """Test how DuckDB's default JSON reader handles 2D arrays."""
    
    # Test data: [[[1, 2], [3, 4]], [[5, 6], [7, 8]]]
    data = [[[1, 2], [3, 4]], [[5, 6], [7, 8]]]
    
    # Create temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        json.dump(data, f)
        temp_file = f.name
    
    try:
        conn = duckdb.connect()
        
        # Test DuckDB's default behavior
        print("Testing DuckDB's default JSON reader...")
        result = conn.execute(f"SELECT * FROM read_json_auto('{temp_file}')").fetchall()
        print(f"DuckDB default result: {result}")
        print(f"Number of rows: {len(result)}")
        
        if result:
            print(f"First row: {result[0]}")
            if len(result) > 1:
                print(f"Second row: {result[1]}")
        
        # Test schema
        schema = conn.execute(f"DESCRIBE SELECT * FROM read_json_auto('{temp_file}')").fetchall()
        print(f"Schema: {schema}")
        
    finally:
        os.unlink(temp_file)

if __name__ == "__main__":
    test_duckdb_2d_array()
